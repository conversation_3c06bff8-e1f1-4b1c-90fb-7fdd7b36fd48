import { nanoid } from 'nanoid';
import { ApplicationController } from 'stimulus-use';

export default class ModularContent extends ApplicationController {
	static targets = ['modules', 'menu', 'search', 'suggest', 'searchinput'];
	static values = {
		modules: Array,
	};

	connect() {
		if (!this.hasModulesValue) {
			console.warn('No modules defined!');
			return;
		}

		const hotKeys = this.modulesValue.reduce((reduced, module, key) => {
			const moduleWithKey = { ...module, key };
			if (module.hotkey) {
				reduced.push(moduleWithKey);
			}

			return reduced;
		}, []);

		this.renderMenu(hotKeys);
		this.renderCategories(this.getCategories());
	}

	addModule(event) {
		event.preventDefault();

		const module = this.modulesValue[event.currentTarget.dataset.key];

		if (module) {
			this.dispatch('addToScheme', {
				scheme: {
					// eslint-disable-next-line no-useless-escape
					[`${module.template}____${nanoid().replace(/\-/gm, '_')}`]: { ...module.scheme, deletable: true, draggable: true },
				},
			});
		}

		this.clearSearch();
	}

	renderCategories(categories) {
		if (!this.hasSuggestTarget) return;

		this.suggestTarget.innerHTML = '';
		categories.forEach(this.renderCategory.bind(this));
	}

	renderCategory(category) {
		if (!category.buttons || category.buttons.length === 0) return;

		const label = document.createElement('div');
		const list = document.createElement('ul');
		label.classList.add('b-suggest__label');
		label.innerHTML = category.label;
		list.setAttribute('class', 'b-suggest__list reset');
		this.suggestTarget.append(label);
		this.suggestTarget.append(list);
		category.buttons.forEach((item) => this.renderSuggestMenuItem(list, item));
	}

	renderMenu(hotKeys) {
		hotKeys.forEach(this.renderMenuItem.bind(this));
		this.renderSearchMenuItem();
	}

	renderMenuItem(item) {
		if (!this.hasMenuTarget) return;

		const menuItem = document.createElement('li');
		menuItem.classList.add('m-icons__item');
		menuItem.innerHTML = `
			<a href="#" class="m-icons__link item-icon" data-action="ModularContent#addModule" data-key="${
				item.key
			}"><span class="item-icon__icon ico ico--${item.icon || 'list'}"></span><span class="item-icon__text">${
			item.scheme.label
		}</span></a>
		`;

		this.menuTarget.append(menuItem);
	}

	renderSearchMenuItem() {
		if (!this.hasMenuTarget) return;

		const menuItem = document.createElement('li');
		menuItem.classList.add('m-icons__item');
		menuItem.innerHTML = `
			<a href="#" class="m-icons__link item-icon" data-action="ModularContent#toggleSearch"><span class="item-icon__icon ico ico--plus"></span><span class="item-icon__text">Další</span></a>
		`;

		this.menuTarget.append(menuItem);
	}

	renderSuggestMenuItem(list, item) {
		const menuItem = document.createElement('li');
		menuItem.classList.add('m-icons__item');
		menuItem.innerHTML = `<li class="b-suggest__item" data-action="click->ModularContent#addModule" data-key="${item.key}">
		<span class="b-suggest__link"><span class="item-icon__icon ico ico--${item.icon || 'list'}"></span><span class="item-icon__text">${
			item.scheme.label
		}</span></span>
		</li>
		`;

		list.append(menuItem);
	}

	toggleSearch(e) {
		if (e) {
			e.preventDefault();
		}

		if (this.hasSearchTarget) {
			this.searchTarget.classList.toggle('u-hide');
			if (!this.searchTarget.classList.contains('u-hide')) {
				this.focusSearch();
			}
		}
	}

	getCategories(searchValue) {
		let filteredCategories = this.modulesValue.map((module, key) => ({ ...module, key }));

		if (searchValue) {
			filteredCategories = filteredCategories.filter((module) => {
				const reg = new RegExp(searchValue, 'gi');

				return reg.test(module.scheme.label);
			});
		}

		return filteredCategories.reduce(
			(grouped, module) => {
				if (module.category) {
					const category = grouped.find((item) => item.label === module.category);
					if (category) {
						category.buttons.push(module);
					} else {
						grouped.unshift({
							label: module.category,
							buttons: [module],
						});
					}
				} else {
					const category = grouped.find((item) => item.label === 'Ostatní');
					category.buttons.push(module);
				}

				return grouped;
			},
			[
				{
					label: 'Ostatní',
					buttons: [],
				},
			],
		);
	}

	focusSearch() {
		if (this.hasSearchinputTarget) {
			this.searchinputTarget.focus();
		}
	}

	clearSearch() {
		if (this.hasSearchinputTarget) {
			this.searchinputTarget.value = '';
			if (this.hasSearchTarget && !this.searchTarget.classList.contains('u-hide')) {
				this.toggleSearch();
			}
			this.renderCategories(this.getCategories());
		}
	}

	filterCategories(e) {
		const value = e.target.value;

		if (value && value.length > 0) {
			this.renderCategories(this.getCategories(value));
		} else {
			this.renderCategories(this.getCategories());
		}
	}

	updateModules(event) {
		const { scheme } = event.detail;
		if (this.hasModulesTarget) {
			this.modulesTarget.value = JSON.stringify(scheme);
		}
	}
}
