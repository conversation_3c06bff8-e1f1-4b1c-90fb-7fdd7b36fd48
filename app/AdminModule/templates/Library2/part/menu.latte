{default $action = 'default'}
{foreach $menu as $t}
	<li class="b-library__tree-item" id="t{$t->id}">
		<span class="b-library__tree-wrapper">
			<a n:href="$action $t->id, 'paramValue'=>$paramValue" data-id="{$t->id}"class="b-library__tree-link{if $t->id == $object->id} is-selected{/if}" data-action="Tree#goTo">
				<span class="item-icon">
					<span class="item-icon__icon icon">
						{include $templates.'/part/icons/folder.svg'}
					</span>
					<span class="item-icon__text">
						{$t->name}
					</span>
				</span>
			</a>
			{if $t->id != 1}
				<span class="b-library__actions">
					<span class="item-icon__icon icon" title="Nová složka" data-action="click->Tree#add">
						{include $templates.'/part/icons/plus-circle.svg'}
					</span>
					<span class="item-icon__icon icon" title="Smazat složku" data-action="click->Tree#delete">
						{include $templates.'/part/icons/trash.svg'}
					</span>
				</span>
			{/if}
		</span>
		{if $t->crossroad->count()}
			<ul class="b-library__tree">
				{include 'menu.latte', 'menu' => $t->crossroad, 'paramValue' => $paramValue, action=>$action}
			</ul>
		{/if}
	</li>
{/foreach}
