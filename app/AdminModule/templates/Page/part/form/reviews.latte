{if !isset($k)} {var $k='XXX'} {/if}
{if !isset($data)} {var $data=array(
		'id'=> NULL,
		'date' => date('Y-m-d H:i:s'),
		'name'=>'',
		'firm'=>'',
		'position'=>'',
		'text' => '',
		'sort' => NULL,
		'created' => $userEntity->id
	)}
{/if}
<li>
	<div class="inner">
		<span class="drag-area js-handle"></span>

		{if $data['id']}
			{php $reviewValue = $data['id']}
		{else}
			{php $reviewValue = $k}
		{/if}

		<div class="grid-row">
			<input type="hidden" name="reviewId[{$reviewValue}]" value="{$data['id']}"  />
			{*<p class="grid-1-5">*}
				{*{$data['date']}*}
				<input type="hidden" name="reviewAuthorId[{$reviewValue}]" value="{$data['created']}"  />
			{*</p>*}
			<p class="grid-1-5">
				<span class="inp-fix">
					<input type="text" class="inp-text w-full" name="reviewName[{$reviewValue}]" value="{$data['name']}" />
				</span>
			</p>
			<p class="grid-1-5">
				<span class="inp-fix">
					<input type="text" class="inp-text w-full" name="reviewPosition[{$reviewValue}]" value="{$data['position']}" />
				</span>
			</p>
			<p class="grid-1-5">
				<span class="inp-fix">
					<input type="text" class="inp-text w-full" name="reviewFirm[{$reviewValue}]" value="{$data['firm']}" />
				</span>
			</p>
			<p class="grid-2-5 center">
				<span class="inp-fix">
					<textarea class="inp-text w-full" name="reviewText[{$reviewValue}]">{$data['text']}</textarea>
				</span>
			</p>

			<input type="hidden" name="reviewSort[{$reviewValue}]" value="{$data['sort']}" class="inp-sort" />


			<div class="grid-row">
				<div class="grid-1-3">

					<a class="btn btn-icon-before thickbox" href="/{$config['adminAlias']}/library/?id=2&paramValue={$reviewValue}&ref=>'page'"
					   data-library="attached-images{$reviewValue}" data-skbox-title="Knihovna">
						<span>
							<span class="icon icon-attachment"></span>
							{_butt_attach_image}
						</span>
					</a>
				</div>
				<div class="grid-1-3">
					<ul class="reset crossroad-images-param-values" id="attached-images{$reviewValue}" data-place="imagesR" style="margin-left: 15px;">
						{if isset($image)}
							<li style="width: auto !important;white-space: nowrap;">
								<div style="display: inline-block;vertical-align: middle;">
									{php $img = $imageResizer->getImg($image->filename, 's')}
									<img src="{$img->src}" alt="" width="50" class="crossroad-contacts" style="max-width: 50px;position: relative;" />
								</div>

								<input type="checkbox" id="imageDelete[{$reviewValue}]" name="imageDelete[{$reviewValue}]" value="1" style="display: inline-block;vertical-align: middle;" />
								<label for="imageDelete[{$reviewValue}]" style="display: inline-block;vertical-align: middle;">{_delete_button}</label>
							</li>
						{/if}
					</ul>
				</div>
			</div>
		</div>
		<a href="#" class="icon icon-close remove"></a>
	</div>
</li>
