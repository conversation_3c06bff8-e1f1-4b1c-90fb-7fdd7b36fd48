{block #content}
	<h3 class="title">Test ACL</h3>
	<div class="grid-row">
		<div class="grid-1-2">
			<table>
				<tr>
					<td class="bold">ID</td>
					<td>{$user->id}</td>
				</tr>
				<tr>
					<td class="bold">My role</td>
					<td>
						{foreach $user->getAuthorizator()->getRoles() as $role}
							<span n:class="$user->isInRole($role) ? green, red">{$role}</span> {sep}|{/sep}
						{/foreach}
					</td>
				</tr>
				<tr>
					<td class="bold">isDeveloper</td>
					<td class="bold">
						<span n:class="$user->isDeveloper() ? green, red">{if $user->isDeveloper()}yes{else}no{/if}</span>
					</td>
				</tr>
				<tr>
					<td class="bold">isAdmin</td>
					<td class="bold">
						<span n:class="$user->isAdmin() ? green, red">{if $user->isAdmin()}yes{else}no{/if}</span>
					</td>
				</tr>
			</table>
		</div>
		<div class="grid-1-2">
			<table>
				<tr>
					<th class="bold">Resouce</th>
					<th class="bold">Actions</th>
				</tr>
				{foreach $user->getAuthorizator()->getResources() as $res}
					{continueIf Nette\Utils\Strings::startsWith($res, 'mutation')}
					<tr>
						<td class="bold">{$res}</td>
						<td>
							<span n:class="$user->isAllowed($res) ? green, red">ALL</span> #
							{foreach ['default', 'add', 'edit', 'delete'] as $action}
								<span n:class="$user->isAllowed($res, $action) ? green, red">{$action}</span> {sep}|{/sep}
							{/foreach}
						</td>
					</tr>
				{/foreach}
			</table>
		</div>
	</div>
{/block}
