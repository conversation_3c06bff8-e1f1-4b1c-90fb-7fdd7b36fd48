<?php declare(strict_types = 1);

namespace SuperKoderi\BucketFilter;

use App\Model\BucketFilter\Parts\Dial;
use App\Model\BucketFilter\Parts\Flag;
use App\Model\BucketFilter\Parts\FlagValues;
use App\Model\BucketFilter\Parts\MainCategory;
use App\Model\BucketFilter\Parts\Range;
use App\Model\Orm;
use App\Model\Parameter;
use App\Model\Tree;
use Elastica\ResultSet;
use stdClass;
use SuperKoderi\ConfigService;

final class FilterResultMapper
{

	public function __construct(
		private readonly Orm $orm,
		private readonly ConfigService$configService,
		private readonly CatalogParameter $catalogParameter,
		private readonly Flag $flagPart,
		private readonly FlagValues $flagValuesPart,
		private readonly Dial $dialPart,
		private readonly MainCategory $mainCategoryPart,
		private readonly Range $rangePart,
	)
	{
	}


	public function convert(Tree $parameterObject, ResultSet $emptyCountRes, ?ResultSet $countRes, array $selectedParameters = [], bool $inSearch = false): stdClass
	{
		$ret = new stdClass();
		$ret->items = [];
		$ret->openedByUser = [];
		$ret->opened = [];

		$ret->nonRoot = (
			isset($selectedParameters['dials']) && $selectedParameters['dials']
			|| isset($selectedParameters['flags']) && $selectedParameters['flags']
			|| isset($selectedParameters['ranges']) && $selectedParameters['ranges']
		);

		$ret->followingCleanFilterParameters = $selectedParameters;
		unset($ret->followingCleanFilterParameters['dials']);
		unset($ret->followingCleanFilterParameters['flags']);
		unset($ret->followingCleanFilterParameters['ranges']);

		$ret = $this->handleRanges($ret, $emptyCountRes, $countRes, $selectedParameters);
		$ret = $this->handleParameters($parameterObject, $ret, $emptyCountRes, $countRes, $selectedParameters);

		if (!$inSearch) {
			$ret = $this->handleFlags($ret, $emptyCountRes, $countRes, $selectedParameters);
			$ret = $this->handleFlagValues($ret, $emptyCountRes, $countRes, $selectedParameters);
		} else {
			$ret = $this->handleMainCategory($ret, $emptyCountRes, $countRes, $selectedParameters);
		}

		$ret->categories = [];
		if (isset($emptyCountRes->getAggregation('categoryAgg')['buckets'])) {

			$categoryAggBuckets = $emptyCountRes->getAggregation('categoryAgg')['buckets'];
			foreach ($categoryAggBuckets as $categoryAggBucket) {
				$ret->categories[$categoryAggBucket['key']] = $categoryAggBucket['doc_count'];
			}
		}

		foreach ($ret->items as $item) {
			if ($item->isOpen) {
				$ret->opened[] = $item->name;
			}

			if ($item->isOpenByUser) {
				$ret->openedByUser[] = $item->name;
			}
		}

		return $ret;
	}


	private function handleParameters(Tree $parameterObject, stdClass $ret, ResultSet $emptyCountRes, ?ResultSet $countRes, array $selectedParameters = []): stdClass
	{
		$parameters = $this->catalogParameter->getPossibleParametersForCatalog($parameterObject);
		$cfSetup = $this->catalogParameter->getParametersCfForFilter($parameterObject);

		$pageCfForParameters = [];
		if ($cfSetup && isset($cfSetup->visibleParameters)) {
			foreach ($cfSetup->visibleParameters as $visibleParameter) {
				if (isset($visibleParameter->parameter->uid) && isset($visibleParameter->parameter)) {
					$pageCfForParameters[$visibleParameter->parameter->uid] = $visibleParameter;
				}
			}
		}

		foreach ($parameters as $parameter) {
			$aggName = $parameter->uid;
			$emptyAgg = $emptyCountRes->getAggregation($aggName . '_agg');
			$pageCfForParameter = (isset($pageCfForParameters[$parameter->uid])) ? $pageCfForParameters[$parameter->uid] : null;

			if ($parameter->type === Parameter::TYPE_MULTISELECT) {
				if ($countRes) {
					$agg = $countRes->getAggregation($aggName . '_agg');
					$aggData = $agg;
				} else {
					$aggData = $emptyAgg;
				}

				$bucketValues = $this->mapBucketToValuesForMultiselect($parameter, $aggData);
				$newDial = $this->dialPart->create($aggName, $selectedParameters, $parameter, $bucketValues, $pageCfForParameter);
				if ($newDial) {
					$ret->items[$aggName] = $newDial;
				}
			} elseif ($parameter->type === Parameter::TYPE_NUMBER) {
				if (isset($pageCfForParameter->numberAsRange) && $pageCfForParameter->numberAsRange) {
					$rangeAggName = $aggName . 'Range_agg';
					$emptyAgg = $emptyCountRes->getAggregation($rangeAggName);
					if ($countRes) {
						$agg = $countRes->getAggregation($rangeAggName);
					} else {
						$agg = $emptyAgg;
					}

					$min = null;
					$max = null;
					if (isset($agg['min']['value'])) {
						$min = $agg['min']['value'];
					}

					if (isset($agg['max']['value'])) {
						$max = $agg['max']['value'];
					}

					if ($min !== null) {
						$min = floor($min);
					}

					if ($max !== null) {
						$max = floor($max);
					}

					$range = $this->rangePart->create($aggName, $selectedParameters, $min, $max, $parameter);
					$ret->items[$rangeAggName] = $range;

				} else {
					$aggName = $parameter->uid;

					if ($countRes) {
						$agg = $countRes->getAggregation($aggName . '_agg');
						$aggData = $agg[$aggName];
					} else {
						$aggData = $emptyAgg[$aggName];
					}

					$emptyAggData = $emptyAgg[$aggName];
					$bucketValues = $this->mapBucketToValues($parameter, $aggData, $emptyAggData);

					$newDial = $this->dialPart->create($aggName, $selectedParameters, $parameter, $bucketValues, $pageCfForParameter);
					if ($newDial) {
						$ret->items[$aggName] = $newDial;
					}
				}
			} else {
				if ($countRes) {
					$agg = $countRes->getAggregation($aggName . '_agg');
					$aggData = $agg[$aggName];
				} else {
					$aggData = $emptyAgg[$aggName];
				}

				$emptyAggData = $emptyAgg[$aggName];

				$bucketValues = $this->mapBucketToValues($parameter, $aggData, $emptyAggData);

				$newDial = $this->dialPart->create($aggName, $selectedParameters, $parameter, $bucketValues, $pageCfForParameter);
				if ($newDial) {
					$ret->items[$aggName] = $newDial;
				}
			}
		}

		return $ret;
	}

	private function handleMainCategory(stdClass $ret, ResultSet $emptyCountRes, ?ResultSet $countRes, array $selectedParameters = []): stdClass
	{
		$aggName = 'mainCategory';
		if ($countRes) {
			$agg = $countRes->getAggregation($aggName . '_agg');
			$aggData = $agg[$aggName];
		} else {
			$emptyAgg = $emptyCountRes->getAggregation($aggName . '_agg');
			$aggData = $emptyAgg[$aggName];
		}

		$bucketValues = $this->mapBucketToCatalogTree($aggData);

		$newDial = $this->mainCategoryPart->create($aggName, $selectedParameters, $bucketValues);
		$ret->items[$aggName] = $newDial;

		return $ret;
	}


	private function mapBucketToValuesForMultiselect(Parameter $parameter, array $aggData): array
	{
		$ret = [];

		foreach ($parameter->options as $parameterValue) {
			$item = new stdClass();
			$item->parameterValue = $parameterValue;

			if (isset($aggData["{$parameter->uid}_{$parameterValue->id}_agg"]['doc_count'])) {
				$item->count = $aggData["{$parameter->uid}_{$parameterValue->id}_agg"]['doc_count'];
			} else {
				$item->count = 0;
			}

			$ret[] = $item;
		}

		return $ret;
	}


	private function mapBucketToValues(Parameter $parameter, mixed $aggData, mixed $emptyAggData): array
	{
		$ret = [];
		$presentValues = [];
		foreach ($emptyAggData['buckets'] as $bucketRow) {
			$presentValues[$bucketRow['key']] = 0;
		}

		foreach ($aggData['buckets'] as $bucketRow) {
			$presentValues[$bucketRow['key']] = $bucketRow['doc_count'];
		}

		$parameterValues = $parameter->options->toCollection()->findBy(['id' => array_keys($presentValues)]);

		foreach ($parameterValues as $parameterValue) {
			$item = new stdClass();
			$item->parameterValue = $parameterValue;
			$item->count = $presentValues[$parameterValue->id];
			$ret[] = $item;
		}

		return $ret;
	}

	private function mapBucketToCatalogTree(mixed $aggData): array
	{
		$ret = [];
		$bucket = $aggData['buckets'];
		foreach ($bucket as $item) {
			$catalogTree = $this->orm->tree->getById($item['key']);
			if ($catalogTree) {
				$value = new stdClass();
				$value->catalogItem = $catalogTree;
				$value->count = $item['doc_count'];

				$ret[] = $value;
			}
		}

		return $ret;
	}


	private function handleFlags(stdClass $ret, ResultSet $emptyCountRes, ?ResultSet $countRes, array $selectedParameters): stdClass
	{
		$setup = $this->configService->get('bucketFilter');

		foreach ($setup['flags'] as $aggName) {
			$newDial = $this->handleFlag($emptyCountRes, $aggName, $countRes, $selectedParameters);
			if ($newDial !== null) {
				$ret->items[$aggName] = $newDial;
			}
		}

		return $ret;
	}


	private function mapBucketToKey(mixed $aggData, bool $fillZeros = false): array
	{
		$bucket = $aggData['buckets'];
		$keyToCount = [];
		foreach ($bucket as $item) {
			if ($fillZeros) {
				$keyToCount[$item['key']] = 0;
			} else {
				$keyToCount[$item['key']] = $item['doc_count'];
			}
		}

		return $keyToCount;
	}



	private function handleFlagValues(stdClass $ret, ResultSet $emptyCountRes, ?ResultSet $countRes, array $selectedParameters): stdClass
	{
		$setup = $this->configService->get('bucketFilter');

		foreach ($setup['flagValues'] as $aggName) {
			$emptyAgg = $emptyCountRes->getAggregation($aggName . '_agg');

			if ($countRes) {
				$agg = $countRes->getAggregation($aggName . '_agg');
				$aggData = $agg[$aggName];
				if (!$aggData) {
					$aggData = $emptyAgg[$aggName];
					$counts = $this->mapBucketToKey($aggData, true);
				} else {
					$counts = $this->mapBucketToKey($aggData);
				}
			} else {
				$aggData = $emptyAgg[$aggName];
				$counts = $this->mapBucketToKey($aggData);

			}

			$newDial = $this->flagValuesPart->create($aggName, $selectedParameters, $counts);
			$ret->items[$aggName] = $newDial;
		}

		return $ret;
	}


	private function handleRanges(stdClass $ret, ResultSet $emptyCountRes, ?ResultSet $countRes, array $selectedParameters): stdClass
	{
		$setup = $this->configService->get('bucketFilter');
		foreach ($setup['ranges'] as $range) {

			$rangeAggName = $range . 'Range_agg';
			$emptyAgg = $emptyCountRes->getAggregation($rangeAggName);
			if ($countRes) {
				$agg = $countRes->getAggregation($rangeAggName);
			} else {
				$agg = $emptyAgg;
			}

			$min = null;
			$max = null;
			if (isset($agg['min']['value'])) {
				$min = $agg['min']['value'];
			}

			if (isset($agg['max']['value'])) {
				$max = $agg['max']['value'];
			}

			if ($min !== null) {
				$min = floor($min);
			}

			if ($max !== null) {
				$max = floor($max);
			}

			$range = $this->rangePart->create($range, $selectedParameters, $min, $max);
			$ret->items[$rangeAggName] = $range;
		}

		return $ret;
	}


	private function handleFlag(ResultSet $emptyCountRes, string $aggName, ?ResultSet $countRes, array $selectedParameters): ?stdClass
	{
		$emptyAgg = $emptyCountRes->getAggregation($aggName . '_agg');

		if ($countRes) {
			$agg = $countRes->getAggregation($aggName . '_agg');
			$aggData = $agg[$aggName];
		} else {
			$aggData = $emptyAgg[$aggName];
		}

		$counts = $this->mapBucketToKey($aggData);

		return $this->flagPart->create($aggName, $selectedParameters, $counts);
	}

}
