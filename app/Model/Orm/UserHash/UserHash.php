<?php

namespace App\Model;

use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Entity\Entity;

/**
 * @property int $id {primary}
 * @property User $user {m:1 User::$hashes}
 * @property string $type {default ''}
 * @property string $hash {default ''}
 * @property DateTimeImmutable $createdTime {default 'now'}
 * @property DateTimeImmutable $validTo {default '+1 day'}
 * @property bool $valid {default true}
 * @property DateTimeImmutable|null $usedTime
 * @property array $data {container JsonContainer}
 */
class UserHash extends Entity
{
	const TYPE_LOST_PASSWORD = 'lostPassword';

	public function isValid(): bool
	{
		$now = new DateTimeImmutable();
		return $this->validTo > $now && $this->valid;
	}
}
