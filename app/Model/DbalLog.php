<?php

namespace SuperKoderi;

use Nette\Database\Connection;
use Nette;
use Nextras\Dbal\ILogger;
use Nextras\Dbal\Result\Result;
use Tracy\Debugger;

class DbalLog implements ILogger {

	use Nette\SmartObject;

	public function __construct(
		private string $absPath,
		private string $file,
		private \Nextras\Dbal\Connection $db,
		private ConfigService $configService,
	) {}

	public function register(): void
	{
		if ($this->configService->get('dbalLog')) {
			$this->purgeLog();
			$this->db->addLogger($this);
		}
	}



    private function purgeLog(): void
    {
        $file = $this->absPath . $this->file . ".log";
        if (file_exists($file)) {
            unlink($file);
        }
    }

	public function onConnect(): void
	{
	}

	public function onDisconnect(): void
	{
	}

	public function onQuery(string $sqlQuery, float $timeTaken, ?Result $result): void
	{
		Debugger::log($sqlQuery, $this->file);
	}

	public function onQueryException(string $sqlQuery, float $timeTaken, ?\Nextras\Dbal\Drivers\Exception\DriverException $exception): void
	{
		Debugger::log('Exception ' . $exception->getMessage(), $this->file);
		Debugger::log($sqlQuery, $this->file);
	}
}
