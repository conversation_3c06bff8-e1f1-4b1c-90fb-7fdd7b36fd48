<?php declare(strict_types = 1);

namespace App\Model\Erp\Connector;

use App\Model\ElasticSearch\Product\Facade;
use App\Model\Erp\MssqlCaller;
use App\Model\EsIndex;
use App\Model\EsIndexRepository;
use App\Model\Mutation;
use App\Model\Orm;
use App\Model\PriceLevel;
use App\Model\ProductModel;
use App\Model\Sentry\SentryLogger;
use App\Model\Stock;
use Contributte\Monolog\LoggerManager;
use InvalidArgumentException;
use Nette\Utils\ArrayHash;
use Nextras\Dbal\Connection;
use Nextras\Dbal\Result\Row;
use SuperKoderi\ConfigService;
use SuperKoderi\Email\ISyncInfoFactory;
use Tracy\Debugger;

class InfosErpConnector extends BaseErpConnector
{

//	private Connection $msConnection;

	private array $productsToNotify = [];

	private EsIndex $esIndex;

	private array $productPriceToNotify = [];
	private int $counter = 0;

	public function __construct(string $name,
		Orm $orm,
		private readonly Connection $dbal,
		private readonly Facade $facade,
		private readonly EsIndexRepository $esIndexRepository,
		private readonly ISyncInfoFactory $syncInfoFactory,
		private readonly ProductModel $productModel,
		private readonly MssqlCaller $mssqlCaller,


		ConfigService $configService,
		LoggerManager $loggerManager,
		SentryLogger $sentryLogger)
	{
		parent::__construct($name, $orm, $configService, $loggerManager, $sentryLogger);
	}

	public function getData(string $action, array $params = []): ArrayHash
	{
		if ($action === self::ACTION_GET_PRODUCTS) {

			$this->orm->setPublicOnly(false);
			$this->buildProductsCache();
			$productCount = $this->readProductCache();
			if ($productCount) {
				$this->solveMissingProducts();
				$this->sendNotice();
			} else {
				throw new \Exception('Zero product read');
			}


			$arrayHash = new ArrayHash();
			$arrayHash->count = $this->counter;
			return $arrayHash;
		}

		throw new InvalidArgumentException('Invalid action');
	}



	public function updateProducts(bool $solveSets = false): void
	{

		$builder = $this->dbal->createQueryBuilder();
		$builder->select('pv.internalValue as internalValue')

			->from('parameter_value as pv')
			->joinLeft('parameter as par', 'pv.parameterId = par.id')
			->joinLeft('product_parameter as pp', 'pp.parameterValueId = pv.id')
			->joinLeft('product as p', 'p.id = pp.productId')
			->andWhere('par.uid = %s', 'intCislo')
			->andWhere('p.isSet = %i', (int) $solveSets)
			->groupBy('internalValue');
//			->test();

//		echo $builder->getQuerySql();

		$rows = $this->dbal->queryByQueryBuilder($builder)->fetchPairs(null, 'internalValue');

		foreach ($rows as $intCislo) {
//			var_dump($intCislo);
			$msQuery = "aei_seznampolozek @slevy = '10;11;12;13;14;15', @kodprovozu = '10', @UkazSarze = 1, @kodysp = '" . $intCislo . "';";
//			var_dump($msQuery);

//
			$cfg = [
				'driver'   => 'pdo',
				'dsn' => 'sqlsrv:dbname=PSVir;host=*************;port=49196',
				'host'     => '*************, 49196',
//				'port'     => '49196',
				'user' => 'eshop',
				'password' => 'eshop',
				'database' => 'PSVir',
				'TrustServerCertificate' => 1,
			];

			$dibi = new \Dibi\Connection($cfg);
			foreach ($dibi->query($msQuery) as $item) {
				var_dump($item);
			};
			die;


			$infosRows = $this->mssqlCaller->callRemoteQuery($msQuery);


			foreach ($infosRows as $infosRow) {
//				var_dump($infosRow);

				if (!isset($infosRow->SarzeSP)) {
					var_dump('Produkt nemá šarži');
					continue;
				}
				if (!isset($infosRow->Kod)) {
					var_dump('Produkt nemá Kod');
					continue;
				}
				$productSarze = $this->sanetizeSarzeNumber(trim($infosRow->SarzeSP));

				if (!$solveSets && is_null($productSarze)) {
					// pokud produkt nemá šarži pokračuj
                    var_dump('Produkt nemá šarži odpovídající masce');
					continue;
				}

				$intCislo = trim($infosRow->Kod);
				if ($solveSets) {
					$productId = $this->getProductIdByCode($intCislo);
				} else {
					$productId = $this->getProductIdByCodeAndSarze($intCislo, $productSarze);
				}


				if ($productId !== null) {
					$productRow = $this->getProductById($productId);
					if ($productRow !== null) {
						$this->updateProductCacheByInfos($solveSets, $productSarze, $productRow, $infosRow);
					}
				} else {
					var_dump(sprintf('Produkt nenalezen podle interního čísla %s a šarže %s', $intCislo, $productSarze));
				}

			}
		}
	}




	public function putData(string $action, array $data): ArrayHash
	{
		// TODO: Implement putData() method.
		return new ArrayHash();
	}

	public function sanetizeSarzeNumber(string $s): ?string
	{
		if ($s === "") {
			return null;
		}

		if (preg_match('/^(m).*([0-9]{4}[a-z]{0,1})$/', $s, $matches)) {
			return $matches[1].$matches[2];
		} else if (preg_match('/.*([0-9]{4})$/', $s, $matches)) {
			return $matches[1];
		} else if (preg_match('/.*([0-9]{4}g[m]{0,1})$/', $s, $matches)) {
			return $matches[1];
		} else if (preg_match('/.*([0-9]{4}m)$/', $s, $matches)) {
			return $matches[1];
		} else if (preg_match('/.*([0-9]{4}\/.*)$/', $s, $matches)) {
			return $matches[1];
		} else if (preg_match('/.*([0-9]{4}n)$/', $s, $matches)) {
			return $matches[1];
		} else if (preg_match('/.*(S[0-9]{3})$/', $s, $matches)) {
			return $matches[1];
		} else if (preg_match('/^([a-z]).*([0-9]{4}[a-z]{0,1})$/', $s, $matches)) {
			return $matches[1].$matches[2];
		}else if (preg_match('/^(.*)$/', $s, $matches)) {
			// vem vsechno co tam je
			return $matches[1];
		}
//		else if (preg_match('/.*(S.*)$/', $s, $matches)) {
//			return $matches[1];
//		}

		return NULL;
	}

	private function getProductIdByCodeAndSarze(string $intCislo, string $productSarze): ?int
	{
		return $this->dbal->query("SELECT pp.productId as productId FROM product_parameter AS pp
			LEFT JOIN parameter AS par ON (pp.parameterId = par.id)
			LEFT JOIN parameter_value AS pv ON (pp.parameterValueId = pv.id)

			JOIN product_parameter AS pp2 ON (pp2.productId = pp.productId)
			LEFT JOIN parameter AS par2 ON (pp2.parameterId = par2.id)
			LEFT JOIN parameter_value AS pv2 ON (pp2.parameterValueId = pv2.id)

			WHERE par.uid = 'intCislo' AND pv.internalValue = %s
			AND  par2.uid = 'batchNumber' AND pv2.internalValue = %s
			",
			$intCislo,
			$productSarze
		)->fetchField();
	}
	private function getProductIdByCode(string $intCislo): ?int
	{
		return $this->dbal->query("SELECT pp.productId as productId FROM product_parameter AS pp
			LEFT JOIN parameter AS par ON (pp.parameterId = par.id)
			LEFT JOIN parameter_value AS pv ON (pp.parameterValueId = pv.id)
			WHERE par.uid = 'intCislo' AND pv.internalValue = %s
			",
			$intCislo
		)->fetchField();
	}


	private function updateProductByCache(Mutation $mutation, Row $productRow, Row $infosCacheRow): void
	{

		var_dump($productRow->id);


		Debugger::log('Product-'.$productRow->id, 'sync');
		Debugger::log(json_encode($infosCacheRow), 'sync');

		$this->dbal->beginTransaction();

		$productId = (int)$productRow->id;
		$amount = (int)$infosCacheRow->amountSum;

		$variantId = $this->dbal->query('select id from product_variant where productId = %i', $productRow->id)->fetchField();
		$priceLevelId = $this->dbal->query('select id from price_level where type = %s', PriceLevel::TYPE_DEFAULT)->fetchField();
		$oldDefaultPrice = $this->dbal->query('select price from product_variant_price
							where productVariantId = %i and mutationId = %i and priceLevelId = %i', $variantId, $mutation->id, $priceLevelId

		)->fetchField();
		$this->updateStore($productRow, $variantId, $amount);

		$originalPrice = (int) round($infosCacheRow->originalPrice);
		$infosPriceVAT = (int)round($infosCacheRow->price);

		if ($infosPriceVAT === 0 && $oldDefaultPrice > 0) {
			$this->productPriceToNotify[$productId] = $productId;
		}

		$actionPrice = $infosPriceVAT;
		if ( ! ($actionPrice > 0 && $actionPrice != $originalPrice)) {
			$actionPrice = 0;
		}

		$this->updateUpdatePriceLevel($variantId, $originalPrice, PriceLevel::TYPE_DEFAULT);

		$this->updateUpdatePriceLevel($variantId, $actionPrice, PriceLevel::TYPE_PRICE_ACTION);
		if ($actionPrice > 0) {
			$isInAction = true;
		} else {
			$isInAction = false;
		}

		$this->updateUpdatePriceLevel($variantId, round($infosCacheRow->price10), PriceLevel::TYPE_PRICE_10);
		$this->updateUpdatePriceLevel($variantId, round($infosCacheRow->price11), PriceLevel::TYPE_PRICE_11);
		$this->updateUpdatePriceLevel($variantId, round($infosCacheRow->price12), PriceLevel::TYPE_PRICE_12);
		$this->updateUpdatePriceLevel($variantId, round($infosCacheRow->price13), PriceLevel::TYPE_PRICE_13);
		$this->updateUpdatePriceLevel($variantId, round($infosCacheRow->price14), PriceLevel::TYPE_PRICE_14);
		$this->updateUpdatePriceLevel($variantId, round($infosCacheRow->price15), PriceLevel::TYPE_PRICE_15);

		$this->markProductAsDone($productId, $isInAction);

		$this->dbal->commitTransaction();

		$this->counter = $this->counter + 1;
		$this->productModel->handleFlags($productRow);


		$this->updateInEs($productId);

	}


	private function updateStore(Row $product, int $variantId, int $amount): void
	{
		$productId = $product->id;
		$infosStoreAmountOriginal = $amount;

		if ($product->isSet === 1) {
			// jedna se o set prodává se do 0
			if ($product->infosStoreAmount > 0 && $amount === 0) {
				// posleme oznameni
				$this->productsToNotify[$productId] = $productId;
			}
		} else {
			// jedna se o normalni produkt -> porovnej se storeLimi
			if ($amount < $product->storeLimit) {
				$amount = 0;
			}
			if ($product->infosStoreAmount > 0 && ($amount === 0)) {
				$this->productsToNotify[$productId] = $productId;
			}
		}


		$this->updateStoreAmounts($amount, $infosStoreAmountOriginal, $productId, $variantId);
	}


	private function updateUpdatePriceLevel(int $variantId, float $price, string $priceLevelType): void
	{
		$priceLevelId = $this->dbal->query('select id from price_level where type = %s', $priceLevelType)->fetchField();
		if ($priceLevelId === null) {
			throw new \LogicException(sprintf('Missing price level for type %s', $priceLevelType));
		}

		$this->dbal->query('UPDATE `product_variant_price` SET `price`= %f WHERE `productVariantId`=%i and priceLevelId = %i;'
			, $price, $variantId, $priceLevelId);
	}

	private function markProductAsDone(int $productId, bool $isInAction): void
	{
		$this->dbal->query('UPDATE `product` SET `syncTime`= now(), isAction = %i WHERE id = %i;', (int)$isInAction, $productId);
	}

	private function markAllProductAsReadyToSync(): void
	{
		$this->dbal->query('UPDATE `product` SET `syncTime`= null;');
	}

	private function buildProductsCache(): void
	{

//		sqlsrv_configure('ClientBufferMaxKBSize', 102400);
//		var_dump(sqlsrv_get_config('ClientBufferMaxKBSize'));
		//sqlsrv_configure('SetLocaleInfo', 0);

//		$this->msConnection = new Connection([
//			'driver'   => $this->sqlsrvDriver,
//			'host'     => '*************',
//			'port'     => '49196',
//			'username' => 'eshop',
//			'password' => 'eshop',
//			'database' => 'PSVir',
//			'TrustServerCertificate' => 1,
//		]);

		$this->dbal->query('TRUNCATE `store_cache`;');
		$this->updateProducts();
		$this->updateProducts(true);
	}

	private function updateProductCacheByInfos(bool $solveSets, ?string $productSarze, Row $productRow, \stdClass $infosRow): void
	{

		if ($infosRow->CenaCenikSDPH === null) {
			$infosRow->CenaCenikSDPH = 0.0;
		}
//		$infosRow->CenaCenikSDPH = str_replace(',', '.', $infosRow->CenaCenikSDPH);

		$data = [
			'productId = %i' => $productRow->id,
			'isSet = %i' => (int)$solveSets,
			'productSarze = %?s' => $productSarze,
			'infosSarze = %?s' => trim($infosRow->SarzeSP),
			'infosCode = %s' => trim($infosRow->Kod),
			'amount = %i' => (int)$infosRow->MnozstviKDispozici,
			'price = %f' => round((float)$infosRow->CenaCelkemVcetneDPH),
			'price10 = %f' => round((float)$infosRow->Sleva10sDph),
			'price11 = %f' => round((float)$infosRow->Sleva11sDph),
			'price12 = %f' => round((float)$infosRow->Sleva12sDph),
			'price13 = %f' => round((float)$infosRow->Sleva13sDph),
			'price14 = %f' => round((float)$infosRow->Sleva14sDph),
			'price15 = %f' => round((float)$infosRow->Sleva15sDph),
			'originalPrice = %f' => round((float)$infosRow->CenaCenikSDPH),
		];

//		var_dump($data);
		$head = implode(', ', array_keys($data));
		$this->dbal->query("REPLACE `store_cache` SET  ". $head, ... array_values($data));

	}

	private function readProductCache(): int
	{
		$mutation = $this->orm->mutation->getDefault();
		$this->esIndex = $this->esIndexRepository->getProductLastActive($mutation);
		$this->markAllProductAsReadyToSync();

		$rows = $this->dbal->query("SELECT *, sum(amount) AS amountSum, productId FROM store_cache
								GROUP BY productId order by productId asc");

		$counts = 0;
		foreach ($rows as $key => $infosCacheRow) {
			$productRow = $this->getProductById($infosCacheRow->productId);
			$this->updateProductByCache($mutation, $productRow, $infosCacheRow);
			$counts++;
		}
		return $counts;
	}


	private function getProductById(int $productId): ?Row
	{
		return $this->dbal->query('select * from product where id = %i', $productId)->fetch();
	}

	private function sendNotice(): void
	{

		$mutation = $this->orm->mutation->getDefault();

		$mailer = $this->syncInfoFactory->create();
		$to = $this->configService->getParam('sync', 'infoMails');
		$from = "";
		$subject = "Synchronizace | " . $this->configService->get('projectName');
		$template = "syncInfo";

		if ($this->productsToNotify !== []) {
			$mailer->send($mutation, $from, $to, $subject, $template, $this->productsToNotify);
		}

		$template = "syncInfoZeroPrice";
		if ($this->productPriceToNotify !== []) {
			$mailer->send($mutation, $from, $to, $subject, $template, $this->productPriceToNotify);
		}
	}

	private function updateInEs(int $productId): void
	{
		$this->facade->updateById($productId, $this->esIndex);
	}

	private function solveMissingProducts(): void
	{
		// produkty které nemaji syncTime oznac jako vyprodane
		$missingProducts = $this->dbal->query('select * from product
				where infosStoreAmount > 0
					and syncTime is null
				  	and isVoucher != 1
				  	and isMikrosvin != 1
		');

		foreach ($missingProducts as $missingProduct) {
			$this->productsToNotify[$missingProduct->id] = $missingProduct->id;

			$variantId = $this->dbal->query('select id from product_variant where productId = %i', $missingProduct->id)->fetchField();
			$this->updateStoreAmounts(0, 0, $missingProduct->id, $variantId);
			$this->productModel->handleFlags($missingProduct);
			$this->updateInEs($missingProduct->id);
		}
	}


	private function updateStoreAmounts(mixed $amount, int $infosStoreAmountOriginal, mixed $productId, int $variantId): void
	{
		$this->dbal->query('UPDATE `product` SET `infosStoreAmount`= %i, `infosStoreAmountOriginal`= %i WHERE id = %i;', $amount, $infosStoreAmountOriginal, $productId);
		$this->dbal->query('UPDATE `stock_supplies` SET `amount`= %i, lastImport = NOW() WHERE `variantId`=%i and stockId = %i;'
			, $amount, $variantId, Stock::DEFAULT_ID);
	}


}
